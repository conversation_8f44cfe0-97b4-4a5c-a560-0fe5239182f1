# Meme Transaction Worker

This microservice worker is responsible for consuming NATS streams related to Meme trading transactions and SOL price updates. It was created to separate the Meme trading functionality from the main `dex-agent` service for better scalability.

## Overview

The `dex-agent-meme-transaction-worker` consumes the following NATS streams:
- `xbit-agency-affiliate` - Affiliate transaction events for Meme trading
- `xbit-agent-sol-price` - SOL price updates for volume calculations

## Architecture

This worker follows the same pattern as the existing `dex-agent-hl-transaction-worker`:

```
cmd/worker_meme_transaction/main.go
├── app.InitializeMemeTransactionWorker()
    ├── Initialize NATS Meme client
    ├── Ensure affiliate stream exists
    ├── Start affiliate transaction worker (runMemeAffiliateTxWorker)
    └── Start SOL price worker (runMemeSolPriceWorker)
```

## NATS Streams and Subjects

### Affiliate Stream
- **Stream**: `xbit-agency-affiliate`
- **Subjects**: 
  - `agency.affiliate.xbit_tx` - Affiliate transaction events
  - `agency.affiliate.price.501424.So11111111111111111111111111111111111111112` - SOL price updates
- **Consumers**:
  - `xbit-agent-affiliate-tx` - For affiliate transactions
  - `xbit-agent-sol-price` - For SOL price updates

## Build and Deploy

### Build Command
```bash
# Build the worker binary
CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main/worker_meme_transaction cmd/worker_meme_transaction/main.go
```

### Docker Build
The worker can be built using the same Docker pattern as other workers:
```dockerfile
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main/worker_meme_transaction cmd/worker_meme_transaction/main.go
```

## Configuration

The worker uses the same configuration as the main application:
- Database connection for processing transactions
- NATS Meme client configuration
- Logging configuration

## Processing Logic

### Affiliate Transaction Processing
1. Consumes messages from `agency.affiliate.xbit_tx` subject
2. Unmarshals `AffiliateTxEventWrapper` containing multiple transaction events
3. For each transaction:
   - Validates essential fields (user_id, etc.)
   - Calls `affiliateService.ProcessAffiliateTransaction()`
   - Processes meme commission and activity cashback
   - Handles trading task completion

### SOL Price Processing
1. Consumes messages from SOL price subject
2. Unmarshals `SolPriceEvent` 
3. Calls `affiliateService.ProcessSolPriceUpdate()`
4. Updates price snapshots for volume calculations

## Error Handling

- Uses manual acknowledgment for NATS messages
- Implements retry logic with exponential backoff
- Continues processing other messages if one fails
- Comprehensive logging for debugging

## Monitoring

The worker logs:
- Message processing statistics (count, processing time)
- Individual transaction processing results
- Error details for failed operations
- NATS connection status

## Testing

Run tests with:
```bash
go test ./internal/task -v -run TestMemeTransaction
```

## Migration from dex-agent

This worker extracts the following functionality from the main `dex-agent` service:
- `AffiliateSubscriberService` NATS consumption
- `DexUserSubscriberService` (if needed for meme transactions)
- Related affiliate transaction and SOL price processing

The main `dex-agent` service can now focus on GraphQL API serving while this worker handles the heavy NATS stream processing.
