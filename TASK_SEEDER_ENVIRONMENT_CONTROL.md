# Task Seeder Environment Control Implementation

## Overview

This implementation adds environment variable control to the task_seeder functionality to prevent accidental execution of test data seeding in production environments while allowing it in development and testing environments.

## Changes Made

### 1. Configuration Structure Updates

#### `config/system.go`
- Added `EnableTaskSeeder bool` field to the System struct
- Maps to `enable-task-seeder` in YAML configuration

#### `config.yaml`
- Added `enable-task-seeder: {{ index . "ENABLE_TASK_SEEDER" | default "false" }}` 
- Defaults to `false` for production safety

### 2. Environment Files Updated

All environment files have been updated with appropriate values:

- **`env/local.env`**: `ENABLE_TASK_SEEDER=true` (development)
- **`env/unstable.env`**: `ENABLE_TASK_SEEDER=true` (testing)
- **`env/staging.env`**: `ENABLE_TASK_SEEDER=true` (staging)
- **`env/docker.env`**: `ENABLE_TASK_SEEDER=true` (docker development)
- **`env/production.env`**: `ENABLE_TASK_SEEDER=false` (production safety)

### 3. Task Seeder Logic Updates

#### `internal/service/activity_cashback/initializer.go`
- Updated `seedInitialData()` to check `global.GVA_CONFIG.System.EnableTaskSeeder`
- Logs appropriate messages when seeding is enabled/disabled
- Gracefully skips task seeding when disabled

#### `internal/service/activity_cashback/admin_service.go`
- Updated `SeedInitialTasks()` to respect the configuration
- Returns early with no error when seeder is disabled

### 4. Manual Reseed Commands Updated

#### `cmd/reseed-tasks/main.go`
- Added safety check before database initialization
- Exits with clear error message when seeder is disabled
- Provides instructions on how to enable seeding

#### `cmd/reseed-trading-tasks/main.go`
- Added safety check before database initialization
- Exits with clear error message when seeder is disabled
- Provides instructions on how to enable seeding

### 5. GraphQL Resolvers Updated

#### `internal/controller/admin/graphql/resolvers/admin_activity_cashback.go`
#### `internal/controller/graphql/resolvers/admin_activity_cashback.go`
- Updated `AdminSeedInitialTasks()` resolvers to check configuration
- Return appropriate error messages when seeder is disabled

### 6. Tests Added

#### `internal/service/activity_cashback/task_seeder_config_test.go`
- Tests configuration loading and behavior
- Verifies seeder respects enabled/disabled states
- Tests environment variable integration

## Usage

### Environment Variable Control

Set the environment variable in your deployment:

```bash
# Enable task seeding (development/testing)
export ENABLE_TASK_SEEDER=true

# Disable task seeding (production)
export ENABLE_TASK_SEEDER=false

# Unset (defaults to false - production safe)
unset ENABLE_TASK_SEEDER
```

### Manual Seeding Commands

When seeder is disabled:
```bash
$ ENABLE_TASK_SEEDER=false go run cmd/reseed-tasks/main.go
⚠️  Task seeder is disabled in configuration (ENABLE_TASK_SEEDER=false)
   To enable task seeding, set ENABLE_TASK_SEEDER=true in your environment
   This is a safety measure to prevent accidental seeding in production
```

When seeder is enabled:
```bash
$ ENABLE_TASK_SEEDER=true go run cmd/reseed-tasks/main.go
# Proceeds with database connection and seeding
```

### Application Startup

The task seeder will automatically:
- Check the `ENABLE_TASK_SEEDER` environment variable
- Skip seeding if disabled (production safe)
- Proceed with seeding if enabled (development/testing)
- Log appropriate messages for debugging

## Safety Features

1. **Default Disabled**: The seeder defaults to `false` when the environment variable is not set
2. **Early Exit**: Manual reseed commands check configuration before database initialization
3. **Clear Messaging**: All components provide clear log messages about seeder status
4. **Production Safety**: Production environment file explicitly sets `ENABLE_TASK_SEEDER=false`
5. **GraphQL Protection**: Admin GraphQL endpoints respect the configuration

## Environment-Specific Behavior

| Environment | ENABLE_TASK_SEEDER | Behavior |
|-------------|-------------------|----------|
| local       | true              | Seeding enabled for development |
| unstable    | true              | Seeding enabled for testing |
| staging     | true              | Seeding enabled for staging tests |
| docker      | true              | Seeding enabled for containerized dev |
| production  | false             | Seeding disabled for safety |

## Testing

Run the configuration tests:
```bash
go test -v ./internal/service/activity_cashback -run TestTaskSeederConfiguration
go test -v ./internal/service/activity_cashback -run TestTaskSeederEnvironmentVariableIntegration
```

## Benefits

1. **Production Safety**: Prevents accidental seeding in production
2. **Development Flexibility**: Allows seeding in development environments
3. **Clear Control**: Simple environment variable control
4. **Graceful Handling**: No errors when disabled, just skips seeding
5. **Comprehensive Coverage**: All seeding entry points respect the configuration
6. **Audit Trail**: Clear logging of seeder status and decisions
