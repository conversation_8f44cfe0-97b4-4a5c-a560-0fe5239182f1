package activity_cashback

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/activity_cashback"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// TaskProgressService implements TaskProgressServiceInterface
type TaskProgressService struct {
	progressRepo      activity_cashback.UserTaskProgressRepositoryInterface
	taskRepo          activity_cashback.ActivityTaskRepositoryInterface
	completionFactory *activity_cashback.TaskCompletionRepositoryFactory
}

// NewTaskProgressService creates a new TaskProgressService
func NewTaskProgressService(
	progressRepo activity_cashback.UserTaskProgressRepositoryInterface,
	taskRepo activity_cashback.ActivityTaskRepositoryInterface,
) TaskProgressServiceInterface {
	return &TaskProgressService{
		progressRepo:      progressRepo,
		taskRepo:          taskRepo,
		completionFactory: activity_cashback.NewTaskCompletionRepositoryFactory(),
	}
}

// GetUserTaskProgress retrieves all task progress for a user
func (s *TaskProgressService) GetUserTaskProgress(ctx context.Context, userID uuid.UUID) ([]model.UserTaskProgress, error) {
	progress, err := s.progressRepo.GetByUserID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user task progress: %w", err)
	}
	return progress, nil
}

// GetTaskProgress retrieves specific task progress for a user
func (s *TaskProgressService) GetTaskProgress(ctx context.Context, userID, taskID uuid.UUID) (*model.UserTaskProgress, error) {
	progress, err := s.progressRepo.GetByUserAndTask(ctx, userID, taskID)
	if err != nil {
		// Don't wrap gorm.ErrRecordNotFound so it can be detected by callers
		if err == gorm.ErrRecordNotFound {
			return nil, err
		}
		return nil, fmt.Errorf("failed to get task progress: %w", err)
	}
	return progress, nil
}

// InitializeTaskProgress creates initial progress record for a user task
func (s *TaskProgressService) InitializeTaskProgress(ctx context.Context, userID, taskID uuid.UUID) (*model.UserTaskProgress, error) {
	// Get task to set initial values
	task, err := s.taskRepo.GetByID(ctx, taskID)
	if err != nil {
		return nil, fmt.Errorf("failed to get task: %w", err)
	}

	// Create initial progress
	progress := &model.UserTaskProgress{
		UserID:        userID,
		TaskID:        taskID,
		Status:        model.TaskStatusNotStarted,
		ProgressValue: 0,
	}

	// Set target value based on task conditions
	if task.Conditions != nil {
		if task.Conditions.RequiredTradeCount != nil {
			progress.TargetValue = task.Conditions.RequiredTradeCount
		} else if task.Conditions.ConsecutiveDays != nil {
			progress.TargetValue = task.Conditions.ConsecutiveDays
		} else if task.Conditions.MinTradingVolume != nil {
			// For accumulated trading tasks, target value should be the milestone amount
			targetVolume := int(*task.Conditions.MinTradingVolume)
			progress.TargetValue = &targetVolume
		} else if len(task.Conditions.ConsecutiveCheckinMilestones) > 0 {
			// For consecutive check-in tasks with milestones, set target to first milestone
			firstMilestone := s.getFirstMilestone(task.Conditions.ConsecutiveCheckinMilestones)
			if firstMilestone != nil {
				progress.TargetValue = &firstMilestone.Days
			}
		}
	}

	// Set default target value if not specified
	if progress.TargetValue == nil {
		defaultTarget := 1
		progress.TargetValue = &defaultTarget
	}

	if err := s.progressRepo.Create(ctx, progress); err != nil {
		return nil, fmt.Errorf("failed to create task progress: %w", err)
	}

	global.GVA_LOG.Info("Task progress initialized",
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()))

	return progress, nil
}

// IncrementProgress increments task progress by a specified amount
func (s *TaskProgressService) IncrementProgress(ctx context.Context, userID, taskID uuid.UUID, increment int) error {
	progress, err := s.GetTaskProgress(ctx, userID, taskID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			progress, err = s.InitializeTaskProgress(ctx, userID, taskID)
			if err != nil {
				return err
			}
		} else {
			return err
		}
	}

	// Update progress
	progress.ProgressValue += increment
	progress.Status = model.TaskStatusInProgress
	progress.UpdatedAt = time.Now()

	// Check if task is completed
	if progress.TargetValue != nil && progress.ProgressValue >= *progress.TargetValue {
		// For automated tasks (like accumulated trading), set status to CLAIMED since points are auto-awarded
		// For manual tasks, this would be COMPLETED until user claims
		progress.Status = model.TaskStatusClaimed
		now := time.Now()
		progress.LastCompletedAt = &now
		progress.CompletionCount++

		// Note: pointsEarned should be set by the calling service that handles point awarding
		// This method only handles progress completion, not point calculation
	}

	if err := s.progressRepo.Update(ctx, progress); err != nil {
		return fmt.Errorf("failed to update task progress: %w", err)
	}

	return nil
}

// IncrementProgressWithPoints increments task progress and updates points earned
func (s *TaskProgressService) IncrementProgressWithPoints(ctx context.Context, userID, taskID uuid.UUID, increment int, pointsEarned int) error {
	progress, err := s.GetTaskProgress(ctx, userID, taskID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			progress, err = s.InitializeTaskProgress(ctx, userID, taskID)
			if err != nil {
				return err
			}
		} else {
			return err
		}
	}

	// Update progress and points earned
	progress.ProgressValue += increment
	progress.PointsEarned += pointsEarned
	progress.Status = model.TaskStatusInProgress
	progress.UpdatedAt = time.Now()

	// Check if task is completed
	if progress.TargetValue != nil && progress.ProgressValue >= *progress.TargetValue {
		// For automated tasks (like accumulated trading), set status to CLAIMED since points are auto-awarded
		// For manual tasks, this would be COMPLETED until user claims
		progress.Status = model.TaskStatusClaimed
		now := time.Now()
		progress.LastCompletedAt = &now
		progress.CompletionCount++
	}

	if err := s.progressRepo.Update(ctx, progress); err != nil {
		return fmt.Errorf("failed to update task progress with points: %w", err)
	}

	global.GVA_LOG.Debug("Task progress updated with points",
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()),
		zap.Int("progress_increment", increment),
		zap.Int("points_earned", pointsEarned),
		zap.Int("total_progress", progress.ProgressValue),
		zap.Int("total_points_earned", progress.PointsEarned))

	return nil
}

// SetProgress sets task progress to a specific value
func (s *TaskProgressService) SetProgress(ctx context.Context, userID, taskID uuid.UUID, value int) error {
	progress, err := s.GetTaskProgress(ctx, userID, taskID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			progress, err = s.InitializeTaskProgress(ctx, userID, taskID)
			if err != nil {
				return err
			}
		} else {
			return err
		}
	}

	// Update progress
	progress.ProgressValue = value
	progress.Status = model.TaskStatusInProgress
	progress.UpdatedAt = time.Now()

	// Check if task is completed
	if progress.TargetValue != nil && progress.ProgressValue >= *progress.TargetValue {
		// For automated tasks (like accumulated trading), set status to CLAIMED since points are auto-awarded
		// For manual tasks, this would be COMPLETED until user claims
		progress.Status = model.TaskStatusClaimed
		now := time.Now()
		progress.LastCompletedAt = &now
		progress.CompletionCount++

		// Note: pointsEarned should be set by the calling service that handles point awarding
		// This method only handles progress completion, not point calculation
	}

	if err := s.progressRepo.Update(ctx, progress); err != nil {
		return fmt.Errorf("failed to update task progress: %w", err)
	}

	return nil
}

// SetProgressWithoutAutoComplete sets progress value without auto-completion logic
func (s *TaskProgressService) SetProgressWithoutAutoComplete(ctx context.Context, userID, taskID uuid.UUID, value int) error {
	progress, err := s.GetTaskProgress(ctx, userID, taskID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			progress, err = s.InitializeTaskProgress(ctx, userID, taskID)
			if err != nil {
				return err
			}
		} else {
			return err
		}
	}

	// Update progress without auto-completion logic
	progress.ProgressValue = value
	progress.Status = model.TaskStatusInProgress
	progress.UpdatedAt = time.Now()

	if err := s.progressRepo.Update(ctx, progress); err != nil {
		return fmt.Errorf("failed to update task progress: %w", err)
	}

	global.GVA_LOG.Debug("Task progress updated without auto-completion",
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()),
		zap.Int("progress_value", value))

	return nil
}

// CompleteProgress marks a task as completed
func (s *TaskProgressService) CompleteProgress(ctx context.Context, userID, taskID uuid.UUID) error {
	return s.CompleteProgressWithPoints(ctx, userID, taskID, 0)
}

// CompleteProgressWithPoints marks a task as completed and sets points earned
func (s *TaskProgressService) CompleteProgressWithPoints(ctx context.Context, userID, taskID uuid.UUID, pointsEarned int) error {
	return s.completeProgressWithPointsInternal(ctx, userID, taskID, pointsEarned, false)
}

// CompleteProgressiveTaskWithPoints marks a progressive task as completed without overriding progressValue
func (s *TaskProgressService) CompleteProgressiveTaskWithPoints(ctx context.Context, userID, taskID uuid.UUID, pointsEarned int) error {
	return s.completeProgressWithPointsInternal(ctx, userID, taskID, pointsEarned, true)
}

// completeProgressWithPointsInternal is the internal implementation
func (s *TaskProgressService) completeProgressWithPointsInternal(ctx context.Context, userID, taskID uuid.UUID, pointsEarned int, isProgressive bool) error {
	progress, err := s.GetTaskProgress(ctx, userID, taskID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			progress, err = s.InitializeTaskProgress(ctx, userID, taskID)
			if err != nil {
				return err
			}
		} else {
			return err
		}
	}

	// Mark as completed and claimed (auto-claim)
	progress.Status = model.TaskStatusClaimed
	now := time.Now()
	progress.LastCompletedAt = &now
	progress.CompletionCount++
	progress.UpdatedAt = now

	// For progressive tasks, don't override progressValue (use streakCount instead)
	// Exception: For accumulated trading tasks, set progress to target value to maintain consistency
	// For normal tasks, set progress to target value
	if !isProgressive && progress.TargetValue != nil {
		progress.ProgressValue = *progress.TargetValue
	} else if isProgressive && progress.TargetValue != nil {
		// Check if this is an accumulated trading task by looking at task properties
		task, err := s.taskRepo.GetByID(ctx, taskID)
		if err == nil && task.ActionTarget != nil && *task.ActionTarget == "memeTrade" && task.Frequency == model.FrequencyProgressive {
			// For accumulated trading tasks, set progress to target value to maintain consistency
			progress.ProgressValue = *progress.TargetValue
			global.GVA_LOG.Debug("Set accumulated trading task progress to target value",
				zap.String("task_id", taskID.String()),
				zap.String("task_identifier", func() string {
					if task.TaskIdentifier != nil {
						return string(*task.TaskIdentifier)
					}
					return "unknown"
				}()),
				zap.Int("target_value", *progress.TargetValue))
		}
	}

	// Set points earned if provided
	if pointsEarned > 0 {
		progress.PointsEarned = pointsEarned
	}

	if err := s.progressRepo.Update(ctx, progress); err != nil {
		return fmt.Errorf("failed to complete task progress: %w", err)
	}

	global.GVA_LOG.Info("Task progress completed",
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()),
		zap.Bool("is_progressive", isProgressive),
		zap.Int("points_earned", pointsEarned))

	return nil
}

// UpdateStreak updates streak count for consecutive tasks
func (s *TaskProgressService) UpdateStreak(ctx context.Context, userID, taskID uuid.UUID, increment bool) error {
	progress, err := s.GetTaskProgress(ctx, userID, taskID)
	if err != nil {
		return err
	}

	if increment {
		progress.StreakCount++
	} else {
		progress.StreakCount = 0
	}

	// For configurable consecutive checkin tasks, let the handler manage status and target
	task, err := s.taskRepo.GetByID(ctx, taskID)
	if err == nil && task.TaskIdentifier != nil &&
		*task.TaskIdentifier == model.TaskIDConsecutiveCheckinConfigurable {

		// Only update progress value to match streak count
		progress.ProgressValue = progress.StreakCount

		// Update LastCompletedAt when user checks in, but don't change status or target
		// The handler will manage status and target value appropriately
		if increment && progress.StreakCount > 0 {
			now := time.Now()
			progress.LastCompletedAt = &now
		}
	}

	progress.UpdatedAt = time.Now()

	if err := s.progressRepo.Update(ctx, progress); err != nil {
		return fmt.Errorf("failed to update streak: %w", err)
	}

	global.GVA_LOG.Info("Streak updated",
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()),
		zap.Int("streak_count", progress.StreakCount))

	return nil
}

// ResetStreak resets streak count to zero
func (s *TaskProgressService) ResetStreak(ctx context.Context, userID, taskID uuid.UUID) error {
	return s.UpdateStreak(ctx, userID, taskID, false)
}

// GetUserStreaks retrieves all tasks with active streaks for a user
func (s *TaskProgressService) GetUserStreaks(ctx context.Context, userID uuid.UUID) ([]model.UserTaskProgress, error) {
	streaks, err := s.progressRepo.GetStreakTasks(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user streaks: %w", err)
	}
	return streaks, nil
}

// GetCompletionStats retrieves completion statistics for a user within a date range
func (s *TaskProgressService) GetCompletionStats(ctx context.Context, userID uuid.UUID, startDate, endDate time.Time) (map[string]int, error) {
	unifiedRepo := s.completionFactory.GetUnifiedRepository()
	stats, err := unifiedRepo.GetUserCompletionStats(ctx, userID, startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get completion stats: %w", err)
	}
	return stats, nil
}

// GetTaskCompletionRate calculates completion rate for a specific task
func (s *TaskProgressService) GetTaskCompletionRate(ctx context.Context, taskID uuid.UUID) (float64, error) {
	// Get total users who have this task
	allProgress, err := s.progressRepo.GetByTaskID(ctx, taskID)
	if err != nil {
		return 0, fmt.Errorf("failed to get task progress: %w", err)
	}

	if len(allProgress) == 0 {
		return 0, nil
	}

	// Count completed tasks
	completedCount := 0
	for _, progress := range allProgress {
		if progress.IsCompleted() {
			completedCount++
		}
	}

	completionRate := float64(completedCount) / float64(len(allProgress)) * 100
	return completionRate, nil
}

// CompleteTaskProgressOnly completes task progress only without awarding points
// Note: This is a placeholder implementation. The actual point awarding will be handled
// by TaskManagementService since TaskProgressService doesn't have access to TierService
func (s *TaskProgressService) CompleteTaskProgressOnly(ctx context.Context, userID, taskID uuid.UUID, verificationData map[string]interface{}) error {
	// Get the task to determine points
	task, err := s.taskRepo.GetByID(ctx, taskID)
	if err != nil {
		return fmt.Errorf("failed to get task: %w", err)
	}

	// Check if task can be completed (for daily tasks and progressive consecutive check-in tasks, check if already completed today)
	if task.Frequency == model.FrequencyDaily ||
		(task.Frequency == model.FrequencyProgressive && task.TaskIdentifier != nil &&
			*task.TaskIdentifier == model.TaskIDConsecutiveCheckinConfigurable) {
		progress, err := s.GetTaskProgress(ctx, userID, taskID)
		if err != nil && err != gorm.ErrRecordNotFound {
			return fmt.Errorf("failed to get task progress: %w", err)
		}

		if progress != nil && progress.LastCompletedAt != nil {
			today := time.Now().Truncate(24 * time.Hour)
			lastCompleted := progress.LastCompletedAt.Truncate(24 * time.Hour)
			if today.Equal(lastCompleted) {
				global.GVA_LOG.Debug("Task already completed today",
					zap.String("user_id", userID.String()),
					zap.String("task_id", taskID.String()),
					zap.Time("last_completed", *progress.LastCompletedAt))
				return nil // Already completed today, no error
			}
		}
	}

	// Complete the progress
	if err := s.CompleteProgress(ctx, userID, taskID); err != nil {
		return fmt.Errorf("failed to complete progress: %w", err)
	}

	// Note: Point awarding will be handled by the calling service
	global.GVA_LOG.Info("Task progress completed via CompleteTaskProgressOnly",
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()),
		zap.Int("points", task.Points))

	return nil
}

// getFirstMilestone returns the milestone with the smallest number of days
func (s *TaskProgressService) getFirstMilestone(milestones []model.ConsecutiveCheckinMilestone) *model.ConsecutiveCheckinMilestone {
	if len(milestones) == 0 {
		return nil
	}

	firstMilestone := &milestones[0]
	for i := 1; i < len(milestones); i++ {
		if milestones[i].Days < firstMilestone.Days {
			firstMilestone = &milestones[i]
		}
	}
	return firstMilestone
}

// getNextMilestone returns the next milestone the user should work towards
func (s *TaskProgressService) getNextMilestone(milestones []model.ConsecutiveCheckinMilestone, currentStreak int) *model.ConsecutiveCheckinMilestone {
	if len(milestones) == 0 {
		return nil
	}

	var nextMilestone *model.ConsecutiveCheckinMilestone
	for i := range milestones {
		if milestones[i].Days > currentStreak {
			if nextMilestone == nil || milestones[i].Days < nextMilestone.Days {
				nextMilestone = &milestones[i]
			}
		}
	}
	return nextMilestone
}

// updateTargetValueForMilestones updates the target value based on the next milestone
func (s *TaskProgressService) updateTargetValueForMilestones(ctx context.Context, progress *model.UserTaskProgress, task *model.ActivityTask) error {
	if task.Conditions == nil || len(task.Conditions.ConsecutiveCheckinMilestones) == 0 {
		return nil
	}

	nextMilestone := s.getNextMilestone(task.Conditions.ConsecutiveCheckinMilestones, progress.StreakCount)
	if nextMilestone != nil {
		progress.TargetValue = &nextMilestone.Days
	} else {
		// All milestones completed, set target to highest milestone
		highestMilestone := s.getHighestMilestone(task.Conditions.ConsecutiveCheckinMilestones)
		if highestMilestone != nil {
			progress.TargetValue = &highestMilestone.Days
		}
	}
	return nil
}

// getHighestMilestone returns the milestone with the largest number of days
func (s *TaskProgressService) getHighestMilestone(milestones []model.ConsecutiveCheckinMilestone) *model.ConsecutiveCheckinMilestone {
	if len(milestones) == 0 {
		return nil
	}

	highestMilestone := &milestones[0]
	for i := 1; i < len(milestones); i++ {
		if milestones[i].Days > highestMilestone.Days {
			highestMilestone = &milestones[i]
		}
	}
	return highestMilestone
}

// calculateMilestoneProgress calculates the progress value for milestone-based consecutive tasks
// This returns the progress toward the current milestone, not the total consecutive days
func (s *TaskProgressService) calculateMilestoneProgress(milestones []model.ConsecutiveCheckinMilestone, currentStreak int) int {
	if len(milestones) == 0 {
		return currentStreak
	}

	// Find the next milestone the user is working toward
	nextMilestone := s.getNextMilestone(milestones, currentStreak)
	if nextMilestone == nil {
		// All milestones completed - find the highest milestone and show completion
		highestMilestone := s.getHighestMilestone(milestones)
		if highestMilestone != nil {
			return highestMilestone.Days
		}
		return currentStreak
	}

	// Find the highest completed milestone
	var completedMilestone *model.ConsecutiveCheckinMilestone
	for i := range milestones {
		if milestones[i].Days <= currentStreak {
			if completedMilestone == nil || milestones[i].Days > completedMilestone.Days {
				completedMilestone = &milestones[i]
			}
		}
	}

	// Calculate progress toward the next milestone
	if completedMilestone != nil {
		// If we just completed a milestone, start fresh toward the next one
		// Progress = (current streak - completed milestone days)
		// But we want to show at least 1 when working toward next milestone
		progressFromCompleted := currentStreak - completedMilestone.Days
		if progressFromCompleted <= 0 {
			return 1 // Just completed a milestone, starting toward next
		}
		return progressFromCompleted
	} else {
		// No milestones completed yet, working toward the first milestone
		return currentStreak
	}
}

// CompleteMilestone handles milestone completion for consecutive check-in tasks
func (s *TaskProgressService) CompleteMilestone(ctx context.Context, userID, taskID uuid.UUID, milestonePoints int) error {
	progress, err := s.GetTaskProgress(ctx, userID, taskID)
	if err != nil {
		return fmt.Errorf("failed to get task progress: %w", err)
	}

	// Increment completion count for milestone achievement
	progress.CompletionCount++

	// Add milestone points to total points earned
	progress.PointsEarned += milestonePoints

	// Update timestamp
	progress.UpdatedAt = time.Now()

	// Update the progress record
	if err := s.progressRepo.Update(ctx, progress); err != nil {
		return fmt.Errorf("failed to update milestone completion: %w", err)
	}

	global.GVA_LOG.Info("Milestone completed",
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()),
		zap.Int("milestone_points", milestonePoints),
		zap.Int("total_points_earned", progress.PointsEarned),
		zap.Int("completion_count", progress.CompletionCount))

	return nil
}

// UpdateConsecutiveCheckinProgress updates progress for consecutive check-in tasks with specific status handling
func (s *TaskProgressService) UpdateConsecutiveCheckinProgress(ctx context.Context, userID, taskID uuid.UUID, currentStreak, targetMilestone int, status model.TaskStatus, pointsEarned int) error {
	progress, err := s.GetTaskProgress(ctx, userID, taskID)
	if err != nil {
		return fmt.Errorf("failed to get task progress: %w", err)
	}

	// Update progress fields
	progress.ProgressValue = currentStreak
	progress.TargetValue = &targetMilestone
	progress.Status = status
	progress.PointsEarned += pointsEarned
	progress.UpdatedAt = time.Now()

	// Update last completed time if milestone is reached (status is CLAIMED)
	if status == model.TaskStatusClaimed {
		now := time.Now()
		progress.LastCompletedAt = &now
		progress.CompletionCount++
	}

	// Update the progress record
	if err := s.progressRepo.Update(ctx, progress); err != nil {
		return fmt.Errorf("failed to update consecutive check-in progress: %w", err)
	}

	global.GVA_LOG.Info("Consecutive check-in progress updated",
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()),
		zap.Int("current_streak", currentStreak),
		zap.Int("target_milestone", targetMilestone),
		zap.String("status", string(status)),
		zap.Int("points_earned", pointsEarned))

	return nil
}

// UpdateConsecutiveCheckinProgressWithoutCompletionIncrement updates progress for consecutive check-in tasks without incrementing CompletionCount
// This is used for milestone transitions where CompletionCount was already incremented
func (s *TaskProgressService) UpdateConsecutiveCheckinProgressWithoutCompletionIncrement(ctx context.Context, userID, taskID uuid.UUID, currentStreak, targetMilestone int, status model.TaskStatus, pointsEarned int) error {
	progress, err := s.GetTaskProgress(ctx, userID, taskID)
	if err != nil {
		return fmt.Errorf("failed to get task progress: %w", err)
	}

	// Update progress fields
	progress.ProgressValue = currentStreak
	progress.TargetValue = &targetMilestone
	progress.Status = status
	progress.PointsEarned += pointsEarned
	progress.UpdatedAt = time.Now()

	// Don't increment CompletionCount or update LastCompletedAt for transitions

	// Update the progress record
	if err := s.progressRepo.Update(ctx, progress); err != nil {
		return fmt.Errorf("failed to update consecutive check-in progress: %w", err)
	}

	global.GVA_LOG.Info("Consecutive check-in progress transitioned",
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()),
		zap.Int("current_streak", currentStreak),
		zap.Int("target_milestone", targetMilestone),
		zap.String("status", string(status)),
		zap.Int("points_earned", pointsEarned))

	return nil
}

// UpdateConsecutiveCheckinProgressWithCompletionCount updates progress for consecutive check-in tasks with specific completion count
// This is used for milestone transitions where we need to set both progress and completion count
func (s *TaskProgressService) UpdateConsecutiveCheckinProgressWithCompletionCount(ctx context.Context, userID, taskID uuid.UUID, currentStreak, targetMilestone int, status model.TaskStatus, pointsEarned int, completionCount int) error {
	progress, err := s.GetTaskProgress(ctx, userID, taskID)
	if err != nil {
		return fmt.Errorf("failed to get task progress: %w", err)
	}

	// Update progress fields
	progress.ProgressValue = currentStreak
	progress.TargetValue = &targetMilestone
	progress.Status = status
	progress.PointsEarned += pointsEarned
	progress.CompletionCount = completionCount
	progress.UpdatedAt = time.Now()

	// Don't update LastCompletedAt for transitions

	// Update the progress record
	if err := s.progressRepo.Update(ctx, progress); err != nil {
		return fmt.Errorf("failed to update consecutive check-in progress: %w", err)
	}

	global.GVA_LOG.Info("Consecutive check-in progress updated with completion count",
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()),
		zap.Int("current_streak", currentStreak),
		zap.Int("target_milestone", targetMilestone),
		zap.String("status", string(status)),
		zap.Int("points_earned", pointsEarned),
		zap.Int("completion_count", completionCount))

	return nil
}
